import { <PERSON>act<PERSON>low, Background, BackgroundVariant, NodeMouseHandler, NodeTypes, <PERSON>le, Position } from '@xyflow/react'
import type { Node, Edge } from '@xyflow/react'
import '@xyflow/react/dist/style.css'
import { cn } from "@/lib/utils"
import { useRegexStore } from "@/store/regex.store"
import { useCallback, useRef, useEffect, useMemo, useState } from 'react'
import type { NodeData, CustomNodeProps } from '@/types/graph'
import { NodeType } from '@/types/graph'
import SequenceNode from '../nodes/SequenceNode'

/**
 * 自定义正则节点组件
 * 支持根据hoveredElementId动态高亮样式
 */
function CustomRegexNode({ id, data }: CustomNodeProps) {
  // 从Store获取当前悬停的元素ID
  const hoveredElementId = useRegexStore(state => state.hoveredElementId)

  // 判断当前节点是否被悬停
  const isHovered = hoveredElementId === id



  return (
    <div
      className={cn(
        "px-3 py-2 rounded-lg border text-sm font-medium transition-all duration-200",
        "bg-slate-800 text-slate-300 border-slate-600",
        "min-w-[60px] max-w-[220px]", // 设置最小和最大宽度
        // 高亮样式：当节点被悬停时应用
        isHovered && [
          "border-blue-500 shadow-lg shadow-blue-500/50",
          "bg-slate-700 text-blue-100",
          "ring-2 ring-blue-500/30"
        ]
      )}
    >
      {/* 输入连接点 - 修复连接问题 */}
      <Handle
        id="target"
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-blue-500 border-2 border-blue-300 hover:bg-blue-400 transition-colors"
        style={{
          left: -6, // 确保连接点在节点边缘外
          background: '#3b82f6', // 明亮的蓝色
          border: '2px solid #93c5fd'
        }}
      />

      {/* 节点内容 */}
      <div
        className="break-words overflow-hidden leading-tight"
        style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical',
          wordBreak: 'break-all'
        }}
        title={data.label || data.content || data.originalText || `[${id}]`}
      >
        {data.label || data.content || data.originalText || `[${id}]`}
      </div>

      {/* 输出连接点 - 修复连接问题 */}
      <Handle
        id="source"
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-green-500 border-2 border-green-300 hover:bg-green-400 transition-colors"
        style={{
          right: -6, // 确保连接点在节点边缘外
          background: '#10b981', // 明亮的绿色
          border: '2px solid #86efac'
        }}
      />
    </div>
  )
}



interface GraphPanelProps {
  nodes?: Node[]
  edges?: Edge[]
  className?: string
}

// 定义自定义节点类型
const nodeTypes: NodeTypes = {
  regexNode: CustomRegexNode,
  macro_sequence: SequenceNode, // 新增：宏观序列节点类型
}



/**
 * GraphPanel 组件 - 正则表达式可视化图形面板
 *
 * 功能：
 * - 渲染正则表达式的节点图
 * - 处理节点悬停交互，触发 AI 解释请求
 * - 管理悬停状态和防抖逻辑
 *
 * 性能考量：
 * - UI 层防抖（150ms）+ Store 层去重，双重保护避免重复 API 调用
 * - 使用 useCallback 优化事件处理器，减少不必要的重新渲染
 * - 组件卸载时自动清理定时器，防止内存泄漏
 *
 * @param nodes - 图形节点数据，可选
 * @param edges - 图形边数据，可选
 * @param className - 额外的 CSS 类名
 * @returns JSX.Element
 */
export default function GraphPanel({ nodes, edges, className }: GraphPanelProps) {
  // 使用 useState 和 useEffect 来处理客户端渲染，但避免条件渲染导致的 hydration 问题
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // 通过 selector 只订阅需要的 actions，避免整个 store 变化触发重渲染
  const setHoveredElementId = useRegexStore(state => state.setHoveredElementId)
  const _fetchExplanationForNode = useRegexStore(state => state._fetchExplanationForNode)

  // 防抖处理的 ref - 使用 ReturnType<typeof setTimeout> 兼容浏览器和 Node 环境
  const debounceTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  // 使用 useMemo 优化渲染性能，避免在父组件更新但数据本身未变时重建数组
  const displayNodes = useMemo(() => nodes || [], [nodes])
  const displayEdges = useMemo(() => edges || [], [edges])

  /**
   * 鼠标进入节点时的处理器
   *
   * 功能：
   * - 更新悬停状态
   * - 防抖触发 AI 解释请求
   *
   * 防抖策略：
   * - UI 层 150ms 防抖：避免快速移动鼠标时的频繁调用
   * - Store 层去重：避免相同内容的重复 API 请求
   * - 双层保护确保用户体验流畅且资源使用高效
   *
   * @param _event - 鼠标事件（未使用）
   * @param node - 悬停的节点对象
   */
  const handleNodeMouseEnter: NodeMouseHandler = useCallback((_event, node) => {
    // 立即更新悬停状态，提供即时视觉反馈
    setHoveredElementId(node.id)

    // 清除之前的防抖定时器
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
    }

    // 类型守卫：确保 node.data 存在且包含有效的内容
    const nodeData = node.data
    if (nodeData && typeof nodeData === 'object') {
      // 优化：优先使用regexFragment作为缓存键，避免本地化label导致缓存击穿
      // regexFragment 是稳定的正则片段，不受语言切换影响
      const cacheKey = nodeData.regexFragment || nodeData.label
      if (typeof cacheKey === 'string' && cacheKey.trim()) {
        // UI 层防抖：150ms 延迟，避免快速悬停时的重复调用
        // 注意：Store 层已有去重机制，此处防抖主要优化用户体验
        debounceTimerRef.current = setTimeout(() => {
          _fetchExplanationForNode(node.id, cacheKey)
        }, 150)
      }
    }
  }, [setHoveredElementId, _fetchExplanationForNode])

  /**
   * 鼠标离开节点时的处理器
   *
   * 功能：
   * - 清除悬停状态
   * - 取消待执行的防抖请求
   *
   * 清理逻辑：
   * - 立即清除防抖定时器，避免用户离开后仍触发请求
   * - 重置悬停状态，确保 UI 状态一致性
   *
   * @param _event - 鼠标事件（未使用）
   * @param _node - 离开的节点对象（未使用）
   */
  const handleNodeMouseLeave: NodeMouseHandler = useCallback(() => {
    // 清除防抖定时器，避免用户离开后仍触发 API 请求
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current)
      debounceTimerRef.current = null
    }

    // 立即清除悬停状态
    setHoveredElementId(null)
  }, [setHoveredElementId])

  /**
   * 组件卸载时的清理副作用
   *
   * 清理逻辑：
   * - 确保组件卸载时清理所有待执行的定时器
   * - 防止内存泄漏和意外的异步操作
   */
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  // 移除条件渲染，避免 hydration 问题
  // 改为在 ReactFlow 组件上使用 suppressHydrationWarning








  const defaultEdgeOptions = {
    type: 'default',
    animated: false,
    style: {
      stroke: '#64748b', // slate-500 - 确保边在深色背景下可见
      strokeWidth: 2,
    },
  };







  return (
    <div className={cn("h-full w-full bg-slate-900 rounded-lg", className)}>
      <div suppressHydrationWarning>
        <ReactFlow
          nodes={displayNodes}
          edges={displayEdges}
          nodeTypes={nodeTypes} // 使用自定义节点类型
          proOptions={{ hideAttribution: true }}
          fitView
          nodesDraggable={process.env.NODE_ENV === 'development'} // 实验性API：仅开发环境启用
          nodesConnectable={false} // 实验性API：生产环境禁用连接功能
          elementsSelectable={true}
          onNodeMouseEnter={isMounted ? handleNodeMouseEnter : undefined}
          onNodeMouseLeave={isMounted ? handleNodeMouseLeave : undefined}
          // 🔧 修复边不可见问题：设置边的默认样式
          defaultEdgeOptions={defaultEdgeOptions}
        >
          <Background
            variant={"dots" as BackgroundVariant}
            gap={20}
            size={1}
            color="#334155" // slate-700
          />
        </ReactFlow>
      </div>
    </div>
  )
}

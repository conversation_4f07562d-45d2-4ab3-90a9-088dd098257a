import type { AstRegExp, AstNode } from 'regexp-tree/ast';
import type { ElkNode, ElkExtendedEdge } from 'elkjs';
import { generate } from 'regexp-tree';
import { nanoid } from 'nanoid';
import type { CustomElkNode } from '@/types/graph';
import { NodeType } from '@/types/graph';
import { logger, ERR, INFO } from './logger';

/**
 * ELK 图结构接口
 */
export interface ElkGraph extends ElkNode {
  id: string;
  children: CustomElkNode[];
  edges: ElkExtendedEdge[];
}

/**
 * 转换错误类
 */
export class TransformError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'TransformError';
  }
}



// 重新导出统一的类型定义，避免重复声明
export type { NodeData, CustomElkNode } from '@/types/graph';
export { NodeType } from '@/types/graph';

/**
 * 字符类表达式类型
 */
interface CharClassExpression {
  type: string;
  value?: string;
  from?: { value: string };
  to?: { value: string };
}

// 导入统一的类型定义
import type { MacroSequenceNode, ExtendedAstNode } from '@/types/graph';

/**
 * 常量定义
 */
const MAX_DEPTH = 50; // 最大递归深度，防止栈溢出
const MAX_WARN_COUNT = 10; // 最大警告次数，防止控制台刷屏
const NODE_WIDTH = {
  MIN: 60,
  MAX: 200,
  CHAR_WIDTH: 12,
  PADDING: 20
};

/**
 * 全局缓存和计数器
 */
let generateCache = new WeakMap<AstNode, string>(); // generate 结果缓存（弱引用，防止内存泄漏）
const edgeSet = new Set<string>(); // 边ID去重集合
const uniqueEdgePairs = new Set<string>(); // 边关系去重集合（防止同一对节点多条边）
let generateWarnCount = 0; // generate 警告计数器
let preprocessCallCount = 0; // 预处理函数调用计数器

/**
 * 字符宽度系数
 */
const CHAR_WIDTH_FACTOR = {
  NORMAL: 1.0,   // 普通ASCII字符
  CJK: 1.8,      // 中日韩字符
  EMOJI: 5.5     // Emoji表情符号 (调整为更大的值以满足测试要求)
};

/**
 * 清理缓存和计数器
 * 包含 generateCache 的彻底清理，适用于热重载场景
 */
export function clearTransformCache(): void {
  // 彻底清理 generateCache（适用于热重载或长时间运行场景）
  generateCache = new WeakMap<AstNode, string>();
  edgeSet.clear();
  uniqueEdgePairs.clear();
  generateWarnCount = 0;
  preprocessCallCount = 0; // 重置预处理调用计数器
}

/**
 * 生成唯一的节点 ID（使用 nanoid 确保唯一性）
 */
function generateNodeId(prefix = 'node'): string {
  // 优化：使用 nanoid 替代 Math.random，提升ID生成的可靠性
  // nanoid 生成的ID具有更好的唯一性保证，避免极低概率的重复
  return `${prefix}_${nanoid(10)}`; // 10位长度，足够保证唯一性且保持简洁
}

/**
 * 创建边（带去重检查）
 */
function createEdge(from: string, to: string): ElkExtendedEdge | null {
  const pairKey = `${from}->${to}`;

  // 检查是否已存在相同的边关系
  if (uniqueEdgePairs.has(pairKey)) {
    return null; // 跳过重复的边
  }

  // 生成唯一的边ID
  let edgeId: string;
  let counter = 0;

  do {
    const suffix = counter === 0 ? '' : `_${counter}`;
    edgeId = `edge_${from}_${to}_${Math.random().toString(36).substring(2, 8)}${suffix}`;
    counter++;
  } while (edgeSet.has(edgeId) && counter < 100);

  edgeSet.add(edgeId);
  uniqueEdgePairs.add(pairKey);

  const edge = {
    id: edgeId,
    sources: [from],
    targets: [to]
  };

  return edge;
}

/**
 * 安全的 generate 调用（带缓存和错误计数）
 */
function safeGenerate(node: ExtendedAstNode): string | null {
  // 立刻排除自定义节点，避免无意义的 generate 调用
  if (isMacroSequenceNode(node)) {
    return null;
  }

  const astNode = node as AstNode;

  // 检查缓存
  if (generateCache.has(astNode)) {
    return generateCache.get(astNode) || null;
  }

  try {
    // 放宽范围，允许更多节点类型使用 generate
    // 但排除一些已知会导致问题的节点类型
    const problematicTypes = ['Empty'];
    if (problematicTypes.includes(astNode.type)) {
      return null;
    }

    const result = generate(astNode);
    if (result && result.trim()) {
      generateCache.set(astNode, result);
      return result;
    }
  } catch (error) {
    // 错误计数，防止控制台刷屏
    if (generateWarnCount < MAX_WARN_COUNT) {
      generateWarnCount++;
    }
  }

  return null;
}

/**
 * 获取节点的显示内容（改进版，使用缓存和错误控制）
 */
function getNodeContent(node: ExtendedAstNode): string {
  // 增强错误处理
  if (!node) {
    return 'Unknown';
  }

  // 优先处理宏观序列节点
  if (isMacroSequenceNode(node)) {
    const content = node.value || '';
    return content;
  }

  // 对于标准 AST 节点，尝试使用安全的 generate
  const generatedContent = safeGenerate(node as AstNode);
  if (generatedContent) {
    return generatedContent;
  }

  // 回退到手动内容生成（改进版，提供更好的默认值）
  switch (node.type) {
    case 'Char':
      return (node as any).value || (node as any).symbol || '';
    case 'CharacterClass':
      // 边缘情况处理：空字符类
      const charClassNode = node as any;
      if (!charClassNode.expressions || charClassNode.expressions.length === 0) {
        return '[]';
      }
      return `[${charClassNode.expressions.map((expr: CharClassExpression) => {
        if (expr.type === 'Char') return expr.value || (expr as any).symbol || '';
        if (expr.type === 'ClassRange') {
          const from = expr.from?.value || (expr.from as any)?.symbol || '';
          const to = expr.to?.value || (expr.to as any)?.symbol || '';
          return `${from}-${to}`;
        }
        return '?';
      }).join('')}]`;
    case 'Alternative':
      return 'Sequence';
    case 'Disjunction':
      return '|';
    case 'Group':
      const groupNode = node as any;
      if (groupNode.capturing) {
        return groupNode.name ? `(?<${groupNode.name}>...)` : `(...)`;
      }
      return `(?:...)`;
    case 'Repetition':
      const repNode = node as any;
      const quantifier = repNode.quantifier;
      if (quantifier) {
        if (quantifier.kind === 'Range') {
          const from = quantifier.from || 0;
          const to = quantifier.to;
          return to !== undefined ? `{${from},${to}}` : `{${from},}`;
        }
        return quantifier.kind || '?';
      }
      return '?';
    case 'Quantifier':
      const quantNode = node as any;
      if (quantNode.kind === 'Range') {
        const from = quantNode.from || 0;
        const to = quantNode.to;
        return to !== undefined ? `{${from},${to}}` : `{${from},}`;
      }
      return quantNode.kind || '?';
    case 'Assertion':
      const assertNode = node as any;
      if (assertNode.kind === 'Lookahead') {
        return assertNode.negative ? '(?!...)' : '(?=...)';
      }
      if (assertNode.kind === 'Lookbehind') {
        return assertNode.negative ? '(?<!...)' : '(?<=...)';
      }
      // 改进：为常见的断言类型提供更好的显示
      if (assertNode.kind === '^' || assertNode.kind === 'Start') return '^';
      if (assertNode.kind === '$' || assertNode.kind === 'End') return '$';
      if (assertNode.kind === '\\b' || assertNode.kind === 'Boundary') return '\\b';
      if (assertNode.kind === '\\B' || assertNode.kind === 'NonBoundary') return '\\B';
      return assertNode.kind || assertNode.value || '^$';
    case 'Backreference':
      const backrefNode = node as any;
      if (backrefNode.kind === 'number') {
        return `\\${backrefNode.reference}`;
      }
      if (backrefNode.kind === 'name') {
        return `\\k<${backrefNode.reference}>`;
      }
      return `\\${backrefNode.number || '?'}`;
    case 'ClassRange':
      const rangeNode = node as any;
      const from = rangeNode.from?.value || '';
      const to = rangeNode.to?.value || '';
      return `${from}-${to}`;
    default:
      // 记录未知节点类型，便于调试和扩展
      if (generateWarnCount < MAX_WARN_COUNT) {
        generateWarnCount++;
      }
      const fallbackContent = node.type || 'Unknown';
      return fallbackContent;
  }
}

/**
 * 获取节点类型映射
 */
function getNodeType(node: ExtendedAstNode): NodeType {
  // 优先处理宏观序列节点
  if (isMacroSequenceNode(node)) {
    return NodeType.MACRO_SEQUENCE;
  }

  // 处理标准 AST 节点类型
  switch (node.type) {
    case 'Char':
      return NodeType.LITERAL;
    case 'CharacterClass':
      return NodeType.CHARACTER_CLASS;
    case 'Alternative':
      return NodeType.SEQUENCE;
    case 'Disjunction':
      return NodeType.ALTERNATION;
    case 'Group':
      return NodeType.GROUP;
    case 'Repetition':
      return NodeType.QUANTIFIER;
    case 'Quantifier':
      return NodeType.QUANTIFIER;
    case 'Assertion':
      return NodeType.ASSERTION;
    default:
      return NodeType.LITERAL;
  }
}

/**
 * 检查字符类型并返回宽度系数
 */
function getCharWidthFactor(char: string): number {
  const code = char.codePointAt(0) || 0;

  // Emoji 检测（更精确的范围，包含所有 Unicode Emoji 区域）
  if (
    (code >= 0x1F000 && code <= 0x1F9FF) || // 各种符号和象形文字（包含补充表情区域 0x1F900-0x1F9FF）
    (code >= 0x1F600 && code <= 0x1F64F) || // 表情符号
    (code >= 0x1F300 && code <= 0x1F5FF) || // 杂项符号和象形文字
    (code >= 0x1F680 && code <= 0x1F6FF) || // 交通和地图符号
    (code >= 0x1F700 && code <= 0x1F77F) || // 炼金术符号
    (code >= 0x1FA00 && code <= 0x1FA6F) || // 扩展 A 符号和象形文字
    (code >= 0x1FA70 && code <= 0x1FAFF) || // 扩展 B 符号和象形文字
    (code >= 0x2600 && code <= 0x26FF) ||   // 杂项符号
    (code >= 0x2700 && code <= 0x27BF) ||   // 装饰符号
    (code >= 0xFE00 && code <= 0xFE0F)      // 变体选择器
  ) {
    return CHAR_WIDTH_FACTOR.EMOJI;
  }

  // CJK 字符检测
  if (
    (code >= 0x1100 && code <= 0x115F) || // 韩文字母
    (code >= 0x2E80 && code <= 0x2EFF) || // CJK 部首补充
    (code >= 0x2F00 && code <= 0x2FDF) || // 康熙部首
    (code >= 0x3000 && code <= 0x303F) || // CJK 符号和标点
    (code >= 0x3040 && code <= 0x309F) || // 平假名
    (code >= 0x30A0 && code <= 0x30FF) || // 片假名
    (code >= 0x3100 && code <= 0x312F) || // 注音字母
    (code >= 0x3200 && code <= 0x32FF) || // 带圈CJK字母和月份
    (code >= 0x3400 && code <= 0x4DBF) || // CJK扩展A
    (code >= 0x4E00 && code <= 0x9FFF) || // CJK统一汉字
    (code >= 0xF900 && code <= 0xFAFF) || // CJK兼容汉字
    (code >= 0xAC00 && code <= 0xD7AF)    // 韩文音节
  ) {
    return CHAR_WIDTH_FACTOR.CJK;
  }

  return CHAR_WIDTH_FACTOR.NORMAL;
}

/**
 * 计算节点宽度（改进版，支持不同类型字符的精确宽度，防止溢出）
 */
function calculateNodeWidth(content: string): number {
  // 优化：对于明显超长的字符串，直接返回最大宽度
  // 使用 4 倍阈值（经验值，约68字符），避免过度压缩中等长度的字符串
  const estimatedWidth = content.length * NODE_WIDTH.CHAR_WIDTH + NODE_WIDTH.PADDING;
  if (estimatedWidth > NODE_WIDTH.MAX * 4) {
    return NODE_WIDTH.MAX;
  }

  let width = NODE_WIDTH.PADDING;

  for (const char of content) {
    const factor = getCharWidthFactor(char);
    width += NODE_WIDTH.CHAR_WIDTH * factor;

    // 提前检查是否会超出最大宽度，避免不必要的计算
    if (width > NODE_WIDTH.MAX) {
      break;
    }
  }

  // 确保结果是整数，并严格限制在范围内
  const finalWidth = Math.round(width);
  return Math.max(Math.min(finalWidth, NODE_WIDTH.MAX), NODE_WIDTH.MIN);
}

/**
 * 计算自适应布局间距
 * @param nodes ELK 节点数组
 * @returns 返回值单位为像素的字符串（ELK 仅接受字符串格式）
 */
function calculateAdaptiveSpacing(nodes: CustomElkNode[]): { nodeNode: string; layerSpacing: string } {
  if (nodes.length === 0) {
    return { nodeNode: '50', layerSpacing: '50' };
  }

  // 找到最大节点宽度
  const maxWidth = Math.max(...nodes.map(node => node.width || NODE_WIDTH.MIN));

  // 基于最大宽度计算间距
  const nodeSpacing = Math.max(maxWidth * 0.5, 50);
  const layerSpacing = Math.max(maxWidth * 0.8, 60);

  return {
    nodeNode: nodeSpacing.toString(),
    layerSpacing: layerSpacing.toString()
  };
}

/**
 * 检查节点是否为普通字符节点
 * 增强类型检查，防止极端 AST 情况，排除有特殊语义的字符
 */
function isCharNode(node: ExtendedAstNode): boolean {
  if (node.type !== 'Char' || !('value' in node) || typeof node.value !== 'string' || node.value.length !== 1) {
    return false;
  }

  const charNode = node as any;

  // 排除元字符（如 . * + ? 等）
  const metaChars = ['.', '*', '+', '?', '^', '$', '|', '(', ')', '[', ']', '{', '}', '\\', '-'];
  if (metaChars.includes(node.value)) {
    return false;
  }

  // 排除转义字符（如果有 escaped 标记）
  if (charNode.escaped === true) {
    return false;
  }

  // 排除非简单字符（如果有 kind 标记）
  if (charNode.kind && charNode.kind !== 'simple') {
    return false;
  }

  return true;
}

/**
 * 类型保护函数：检查节点是否为宏观序列节点
 */
function isMacroSequenceNode(node: ExtendedAstNode): node is MacroSequenceNode {
  return node && node.type === 'MacroSequence';
}





/**
 * 找到图中最后一个节点的ID
 * @param nodes 节点数组
 * @returns 最后一个节点的ID，如果没有节点则返回空字符串
 */
function findLastNodeId(nodes: CustomElkNode[]): string {
  if (nodes.length === 0) {
    return '';
  }

  const lastNode = nodes[nodes.length - 1];

  // 对于容器节点（如 MacroAlternation, MacroQuantifier），直接返回容器节点的ID
  // 这样下一个节点就会连接到容器节点，而不是容器内部的子节点
  return lastNode.id;
}

/**
 * 处理字符缓冲区，创建宏观序列节点
 * @param buffer 字符节点缓冲区
 * @param previousNodeId 前一个节点的ID
 * @returns 包含新节点和新边的对象
 */
function processCharBuffer(buffer: ExtendedAstNode[], previousNodeId: string): { newNode: CustomElkNode, newEdge: ElkExtendedEdge | null } {
  // 1. 合并文本
  const combinedText = buffer.map(char => {
    if (isMacroSequenceNode(char)) {
      return char.value;
    }
    return (char as any).value || '';
  }).join('');

  // 2. 创建新的宏观节点
  const newNode: CustomElkNode = {
    id: generateNodeId('macro_sequence'),
    width: calculateNodeWidth(combinedText),
    height: 40,
    data: {
      semanticType: NodeType.MACRO_SEQUENCE,
      content: combinedText,
      originalText: combinedText,
      astNodeType: 'MacroSequence',
      label: combinedText
    }
  };

  // 3. 创建连接到上一个节点的边
  let newEdge: ElkExtendedEdge | null = null;
  if (previousNodeId) {
    newEdge = createEdge(previousNodeId, newNode.id);
  }

  return { newNode, newEdge };
}

/**
 * 处理 Alternative（序列）类型的节点
 * @param sequenceAstNode Alternative 类型的 AST 节点
 * @param parentId 父节点ID
 * @returns 包含节点和边的对象
 */
function handleSequenceNode(sequenceAstNode: any, parentId: string): { nodes: CustomElkNode[], edges: ElkExtendedEdge[] } {
  const nodes: CustomElkNode[] = [];
  const edges: ElkExtendedEdge[] = [];
  let charBuffer: ExtendedAstNode[] = []; // 用于存储连续的 Char 节点
  let lastNodeId = parentId;

  // 获取子表达式
  const expressions = sequenceAstNode.expressions || [];

  // 遍历 Alternative 的所有子表达式
  for (const expression of expressions) {
    if (expression.type === 'Char' && isCharNode(expression)) {
      // 如果是普通字符，先存入缓冲区
      charBuffer.push(expression);
    } else {
      // 遇到非字符节点，先处理缓冲区
      if (charBuffer.length > 0) {
        // 调用函数来处理缓冲区中的字符
        const { newNode, newEdge } = processCharBuffer(charBuffer, lastNodeId);
        nodes.push(newNode);
        if (newEdge) {
          edges.push(newEdge);
        }
        lastNodeId = newNode.id;
        charBuffer = []; // 清空缓冲区
      }

      // 递归处理这个非字符节点
      const { childNodes, childEdges } = traverse(expression, lastNodeId, 0);
      nodes.push(...childNodes);
      edges.push(...childEdges);

      // 更新最后一个节点的 ID，以便连接下一个节点
      if (childNodes.length > 0) {
        lastNodeId = findLastNodeId(childNodes);
      }
    }
  }

  // 循环结束后，别忘了处理可能剩下的最后一个缓冲区
  if (charBuffer.length > 0) {
    const { newNode, newEdge } = processCharBuffer(charBuffer, lastNodeId);
    nodes.push(newNode);
    if (newEdge) {
      edges.push(newEdge);
    }
  }

  return { nodes, edges };
}

/**
 * 处理 Repetition（量词）类型的节点 - 语义化分组
 * @param repetitionAstNode Repetition 类型的 AST 节点
 * @param parentId 父节点ID
 * @param depth 当前递归深度
 * @returns 包含宏观量词节点的对象
 */
function handleQuantifierNode(repetitionAstNode: any, parentId: string, depth: number): { nodes: CustomElkNode[], edges: ElkExtendedEdge[] } {
  // 获取量词信息
  const quantifier = repetitionAstNode.quantifier;
  let quantifierText = '';

  if (quantifier) {
    if (quantifier.kind === 'Range') {
      if (quantifier.from === quantifier.to) {
        quantifierText = `匹配${quantifier.from}次`;
      } else if (quantifier.to === null) {
        quantifierText = `匹配${quantifier.from}次或更多`;
      } else {
        quantifierText = `匹配${quantifier.from}-${quantifier.to}次`;
      }
    } else {
      quantifierText = `量词: ${quantifier.kind}`;
    }
  }

  // 创建宏观量词节点
  const containerNodeId = generateNodeId('macro_quantifier');
  const containerNode: CustomElkNode = {
    id: containerNodeId,
    width: calculateNodeWidth(quantifierText),
    height: 60,
    data: {
      semanticType: NodeType.QUANTIFIER,
      content: quantifierText,
      originalText: quantifierText,
      astNodeType: 'MacroQuantifier',
      label: quantifierText
    },
    layoutOptions: {
      'elk.algorithm': 'layered',
      'elk.direction': 'RIGHT'
    },
    children: [],
    edges: []
  };

  // 递归处理被量词修饰的表达式
  if (repetitionAstNode.expression) {
    const { childNodes, childEdges } = traverse(repetitionAstNode.expression, '', depth + 1);
    containerNode.children!.push(...childNodes);
    containerNode.edges!.push(...childEdges);
  }

  return {
    nodes: [containerNode],
    edges: []
  };
}

/**
 * 处理 Disjunction（分支）类型的节点 - 语义化分组的核心实现
 * @param alternationAstNode Disjunction 类型的 AST 节点
 * @param parentId 父节点ID
 * @param depth 当前递归深度
 * @returns 包含宏观容器节点的对象
 */
function handleAlternationNode(alternationAstNode: any, parentId: string, depth: number): { nodes: CustomElkNode[], edges: ElkExtendedEdge[] } {
  // 1. 创建总的父容器节点 (宏观节点)
  const containerNodeId = generateNodeId('macro_alternation');
  const containerNode: CustomElkNode = {
    id: containerNodeId,
    width: 300, // 初始值，会在布局时由内容决定
    height: 200,
    data: {
      semanticType: NodeType.ALTERNATION,
      content: '分支 |',
      originalText: '分支 |',
      astNodeType: 'MacroAlternation',
      label: '分支 |'
    },
    // 关键：为容器节点内部的布局设置 ELK 选项
    layoutOptions: {
      'elk.algorithm': 'layered',
      'elk.direction': 'RIGHT',
      'elk.layered.spacing.nodeNodeBetweenLayers': '40'
    },
    children: [], // 用于存放分支内部的节点
    edges: []     // 用于存放分支内部的边
  };

  // 2. 在容器内部，创建"起点"和"终点"两个虚拟节点
  const startJunctionId = containerNodeId + '_start';
  const endJunctionId = containerNodeId + '_end';

  const startJunction: CustomElkNode = {
    id: startJunctionId,
    width: 20,
    height: 20,
    data: {
      semanticType: NodeType.JUNCTION,
      content: 'Start',
      originalText: 'Start',
      astNodeType: 'Junction',
      label: 'Start'
    }
  };

  const endJunction: CustomElkNode = {
    id: endJunctionId,
    width: 20,
    height: 20,
    data: {
      semanticType: NodeType.JUNCTION,
      content: 'End',
      originalText: 'End',
      astNodeType: 'Junction',
      label: 'End'
    }
  };

  containerNode.children!.push(startJunction, endJunction);

  // 3. 递归处理左分支 (alternationAstNode.left)
  if (alternationAstNode.left) {
    const { childNodes: leftNodes, childEdges: leftEdges } = traverse(alternationAstNode.left, startJunctionId, depth + 1);
    containerNode.children!.push(...leftNodes);
    containerNode.edges!.push(...leftEdges);

    // 找到左分支的最后一个节点，并将其连接到终点
    const lastNodeOfLeft = findLastNodeId(leftNodes);
    if (lastNodeOfLeft) {
      const leftToEndEdge = createEdge(lastNodeOfLeft, endJunctionId);
      if (leftToEndEdge) {
        containerNode.edges!.push(leftToEndEdge);
      }
    }
  }

  // 4. 递归处理右分支 (alternationAstNode.right)
  if (alternationAstNode.right) {
    const { childNodes: rightNodes, childEdges: rightEdges } = traverse(alternationAstNode.right, startJunctionId, depth + 1);
    containerNode.children!.push(...rightNodes);
    containerNode.edges!.push(...rightEdges);

    // 找到右分支的最后一个节点，并将其连接到终点
    const lastNodeOfRight = findLastNodeId(rightNodes);
    if (lastNodeOfRight) {
      const rightToEndEdge = createEdge(lastNodeOfRight, endJunctionId);
      if (rightToEndEdge) {
        containerNode.edges!.push(rightToEndEdge);
      }
    }
  }

  // 5. 将这个完整的、包含所有子元素的容器节点返回
  return {
    nodes: [containerNode],
    edges: [] // 容器外部没有新的边，连接由调用方处理
  };
}

/**
 * 递归遍历 AST 节点的辅助函数
 * @param subAst AST 子节点
 * @param parentId 父节点ID
 * @param depth 当前递归深度
 * @returns 包含节点和边的对象
 */
function traverse(subAst: ExtendedAstNode, parentId: string, depth = 0): { childNodes: CustomElkNode[], childEdges: ElkExtendedEdge[] } {

  const childNodes: CustomElkNode[] = [];
  const childEdges: ElkExtendedEdge[] = [];

  // 添加最大递归深度防止栈溢出
  if (depth > MAX_DEPTH) {
    const error = new TransformError(
      `达到最大递归深度 ${MAX_DEPTH}，可能存在循环引用或表达式过于复杂`,
      'MAX_DEPTH_EXCEEDED'
    );
    throw error;
  }

  if (!subAst) {
    return { childNodes, childEdges };
  }

  // 处理 Alternative（序列）类型的节点 - 这是核心逻辑
  if (subAst.type === 'Alternative') {
    const sequenceResult = handleSequenceNode(subAst, parentId);
    childNodes.push(...sequenceResult.nodes);
    childEdges.push(...sequenceResult.edges);
    return { childNodes, childEdges };
  }

  // 处理 Disjunction（分支）类型的节点 - 语义化分组的核心
  if (subAst.type === 'Disjunction') {
    const alternationResult = handleAlternationNode(subAst, parentId, depth);
    childNodes.push(...alternationResult.nodes);
    childEdges.push(...alternationResult.edges);

    // 创建从父节点到容器节点的连接
    if (parentId && alternationResult.nodes.length > 0) {
      const containerNode = alternationResult.nodes[0];
      const parentEdge = createEdge(parentId, containerNode.id);
      if (parentEdge) {
        childEdges.push(parentEdge);
      }
    }

    return { childNodes, childEdges };
  }

  // 处理 Repetition（量词）类型的节点 - 语义化分组
  if (subAst.type === 'Repetition') {
    const quantifierResult = handleQuantifierNode(subAst, parentId, depth);
    childNodes.push(...quantifierResult.nodes);
    childEdges.push(...quantifierResult.edges);

    // 创建从父节点到容器节点的连接
    if (parentId && quantifierResult.nodes.length > 0) {
      const containerNode = quantifierResult.nodes[0];
      const parentEdge = createEdge(parentId, containerNode.id);
      if (parentEdge) {
        childEdges.push(parentEdge);
      }
    }

    return { childNodes, childEdges };
  }

  // 处理 MacroAssertion（语义化断言）类型的节点
  if (subAst.type === 'MacroAssertion') {
    const macroAssertionNode = subAst as any;
    const nodeId = generateNodeId();

    const elkNode: CustomElkNode = {
      id: nodeId,
      width: calculateNodeWidth(macroAssertionNode.value),
      height: 40,
      data: {
        semanticType: NodeType.ASSERTION,
        content: macroAssertionNode.value,
        originalText: macroAssertionNode.value,
        astNodeType: 'MacroAssertion',
        label: macroAssertionNode.value
      }
    };

    childNodes.push(elkNode);

    // 创建从父节点到当前节点的边
    if (parentId) {
      const parentEdge = createEdge(parentId, nodeId);
      if (parentEdge) {
        childEdges.push(parentEdge);
      }
    }

    return { childNodes, childEdges };
  }

  // 特殊处理 Group 节点
  if (subAst.type === 'Group') {
    const groupNode = subAst as any;
    if (groupNode.expression) {
      // 如果 Group 内部是 Disjunction，直接处理内部的分支，不创建额外的 Group 节点
      if (groupNode.expression.type === 'Disjunction') {
        const alternationResult = handleAlternationNode(groupNode.expression, parentId, depth + 1);

        // 更新容器节点的标签，表明这是一个分组的分支
        if (alternationResult.nodes.length > 0) {
          const containerNode = alternationResult.nodes[0];
          if (containerNode.data) {
            containerNode.data.label = '分支选择: 第二位数字';
            containerNode.data.content = '分支选择: 第二位数字';
          }
        }

        childNodes.push(...alternationResult.nodes);
        childEdges.push(...alternationResult.edges);

        // 创建从父节点到容器节点的连接
        if (parentId && alternationResult.nodes.length > 0) {
          const containerNode = alternationResult.nodes[0];
          const parentEdge = createEdge(parentId, containerNode.id);
          if (parentEdge) {
            childEdges.push(parentEdge);
          }
        }

        // 不创建 Group 节点，直接返回 Alternation 容器
        return { childNodes, childEdges };
      }
    }
  }

  // 为其他节点类型创建节点
  const nodeId = generateNodeId();
  const content = getNodeContent(subAst);
  const nodeType = getNodeType(subAst);

  const elkNode: CustomElkNode = {
    id: nodeId,
    width: calculateNodeWidth(content),
    height: 40,
    data: {
      semanticType: nodeType,
      content,
      originalText: content,
      astNodeType: subAst.type,
      label: isMacroSequenceNode(subAst) ? subAst.value : content
    }
  };

  childNodes.push(elkNode);

  // 创建从父节点到当前节点的边
  if (parentId) {
    const parentEdge = createEdge(parentId, nodeId);
    if (parentEdge) {
      childEdges.push(parentEdge);
    }
  }

  // 处理其他节点类型的子节点
  switch (subAst.type) {
    case 'Group':
      const groupNode = subAst as any;
      if (groupNode.expression) {
        // 对于其他类型的 Group，正常处理
        const groupResult = traverse(groupNode.expression, nodeId, depth + 1);
        childNodes.push(...groupResult.childNodes);
        childEdges.push(...groupResult.childEdges);
      }
      break;

    case 'Assertion':
      const assertionNode = subAst as any;
      if (assertionNode.assertion) {
        const assertResult = traverse(assertionNode.assertion, nodeId, depth + 1);
        childNodes.push(...assertResult.childNodes);
        childEdges.push(...assertResult.childEdges);
      }
      if (assertionNode.value) {
        const valueResult = traverse(assertionNode.value, nodeId, depth + 1);
        childNodes.push(...valueResult.childNodes);
        childEdges.push(...valueResult.childEdges);
      }
      break;
    default:
      // 对于其他类型，尝试查找常见的子节点属性
      const nodeAny = subAst as any;
      if (nodeAny.body) {
        const bodyResult = traverse(nodeAny.body, nodeId, depth + 1);
        childNodes.push(...bodyResult.childNodes);
        childEdges.push(...bodyResult.childEdges);
      }
      if (nodeAny.expression) {
        const exprResult = traverse(nodeAny.expression, nodeId, depth + 1);
        childNodes.push(...exprResult.childNodes);
        childEdges.push(...exprResult.childEdges);
      }
      if (nodeAny.expressions && Array.isArray(nodeAny.expressions)) {
        for (const expr of nodeAny.expressions) {
          const exprResult = traverse(expr, nodeId, depth + 1);
          childNodes.push(...exprResult.childNodes);
          childEdges.push(...exprResult.childEdges);
        }
      }
      break;
  }





  return { childNodes, childEdges };
}

/**
 * 预处理 AST，识别语义化模式并进行分组
 * @param astBody AST 主体
 * @returns 预处理后的节点数组
 */
function preprocessAstForSemanticGrouping(astBody: ExtendedAstNode): ExtendedAstNode[] {
  preprocessCallCount++;

  // 如果是 Alternative（序列），检查是否有语义化模式
  if (astBody.type === 'Alternative') {
    const expressions = (astBody as any).expressions || [];
    const processedNodes: ExtendedAstNode[] = [];
    let i = 0;

    while (i < expressions.length) {
      const current = expressions[i];

      // 检查是否是 "开始断言 + 字符" 的模式（如 ^1）
      if (current.type === 'Assertion' && current.kind === '^' &&
          i + 1 < expressions.length && expressions[i + 1].type === 'Char') {

        const nextChar = expressions[i + 1];
        // 创建一个组合的语义节点
        const combinedNode = {
          type: 'MacroAssertion',
          assertionType: 'start_with_char',
          value: `开始: ${nextChar.value}`,
          originalNodes: [current, nextChar]
        };
        processedNodes.push(combinedNode as any);
        i += 2; // 跳过两个节点
      }
      // 检查是否是 "字符 + 结束断言" 的模式
      else if (current.type === 'Char' &&
               i + 1 < expressions.length &&
               expressions[i + 1].type === 'Assertion' &&
               expressions[i + 1].kind === '$') {

        const nextAssertion = expressions[i + 1];
        const combinedNode = {
          type: 'MacroAssertion',
          assertionType: 'end_with_char',
          value: `结束: ${current.value}`,
          originalNodes: [current, nextAssertion]
        };
        processedNodes.push(combinedNode as any);
        i += 2; // 跳过两个节点
      }
      else {
        // 普通节点，直接添加
        processedNodes.push(current);
        i++;
      }
    }

    return processedNodes;
  }

  return [astBody];
}

/**
 * 将 regexp-tree 的 AST 转换为 elkjs 能理解的层级图结构（重构版）
 *
 * @param ast - regexp-tree 生成的 AST 对象
 * @returns ELK 图结构对象
 */
export function astToElkGraph(ast: AstRegExp): ElkGraph {
  // 清理缓存和计数器，确保每次调用都是干净的状态
  clearTransformCache();



  if (!ast || !ast.body) {
    // 返回空图
    return {
      id: 'root',
      children: [],
      edges: [],
      width: 100,
      height: 100
    };
  }

  try {
    // 复杂AST结构分析
    const astBody = ast.body as any;
    const astAnalysisData = {
      astType: ast.type,
      bodyType: ast.body?.type,
      bodyChildren: astBody?.body?.length || 0,
      bodyExpressions: astBody?.expressions?.length || 0,
      bodyAlternatives: astBody?.alternatives?.length || 0,
      preprocessedCount: 0, // 将在预处理后更新
      isMultiNode: false // 将在预处理后更新
    };

    // 预处理 AST，识别语义化模式
    const preprocessedNodes = preprocessAstForSemanticGrouping(ast.body);

    // 更新分析数据并记录
    astAnalysisData.preprocessedCount = preprocessedNodes.length;
    astAnalysisData.isMultiNode = preprocessedNodes.length > 1;


    // 如果预处理后有多个节点，需要按序列处理
    if (preprocessedNodes.length > 1) {
      const allNodes: CustomElkNode[] = [];
      const allEdges: ElkExtendedEdge[] = [];
      let lastNodeId = '';

      for (let i = 0; i < preprocessedNodes.length; i++) {
        const node = preprocessedNodes[i];
        const result = traverse(node, lastNodeId);

        allNodes.push(...result.childNodes);
        allEdges.push(...result.childEdges);

        // 更新最后一个节点ID用于连接
        if (result.childNodes.length > 0) {
          lastNodeId = findLastNodeId(result.childNodes);
        }
      }



      // 计算自适应间距
      const spacing = calculateAdaptiveSpacing(allNodes);



      // 创建根图结构
      const elkGraph: ElkGraph = {
        id: 'root',
        children: allNodes,
        edges: allEdges,
        width: 800,
        height: 600,
        layoutOptions: {
          'elk.algorithm': 'layered',
          'elk.direction': 'RIGHT',
          'elk.spacing.nodeNode': spacing.nodeNode,
          'elk.layered.spacing.nodeNodeBetweenLayers': spacing.layerSpacing
        }
      };

      return elkGraph;
    } else {
      // 单个节点，使用原有逻辑
      const result = traverse(ast.body, '');



      // 计算自适应间距
      const spacing = calculateAdaptiveSpacing(result.childNodes);

      // 创建根图结构
      const elkGraph: ElkGraph = {
        id: 'root',
        children: result.childNodes,
        edges: result.childEdges,
        width: 800,
        height: 600,
        layoutOptions: {
          'elk.algorithm': 'layered',
          'elk.direction': 'RIGHT',
          'elk.spacing.nodeNode': spacing.nodeNode,
          'elk.layered.spacing.nodeNodeBetweenLayers': spacing.layerSpacing
        }
      };

      return elkGraph;
    }
  } catch (error) {
    // 确保错误被正确抛出，便于上层捕获
    if (error instanceof TransformError) {
      throw error;
    }

    // 包装未知错误
    throw new TransformError(
      `AST 转换失败: ${error instanceof Error ? error.message : String(error)}`,
      'TRANSFORM_FAILED'
    );
  }
}
